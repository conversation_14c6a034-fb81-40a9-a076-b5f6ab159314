class_name CombatComponent
extends Area2D

@export var combat_owner: Node2D

var overlapping_enemies: Array[CombatComponent] = []

func _ready():
	area_entered.connect(_on_area_entered)
	area_exited.connect(_on_area_exited)

func process_combat(delta: float):
	if overlapping_enemies.is_empty():
		return

	for i in range(overlapping_enemies.size() - 1, -1, -1):
		var enemy_component = overlapping_enemies[i]

		if not is_instance_valid(enemy_component) or not is_instance_valid(enemy_component.combat_owner):
			overlapping_enemies.remove_at(i)
			continue

		var enemy = enemy_component.combat_owner

		if combat_owner.mass <= enemy.mass:
			continue

		var mass_difference = combat_owner.mass - enemy.mass
		var difference_based_rate = (1.0 + mass_difference * 0.1)

		var percentage_based_rate = enemy.mass * GameSettings.min_combat_drain_percentage

		var base_drain_rate = max(difference_based_rate, percentage_based_rate)

		var final_drain_rate = base_drain_rate * GameSettings.combat_speed_multiplier

		var drain_amount = final_drain_rate * delta
		enemy.mass -= drain_amount
		combat_owner.mass -= drain_amount


func _on_area_entered(area: Area2D):
	if area is CombatComponent:
		var enemy_component = area as CombatComponent
		if not overlapping_enemies.has(enemy_component):
			overlapping_enemies.append(enemy_component)


func _on_area_exited(area: Area2D):
	if area is CombatComponent:
		var enemy_component = area as CombatComponent
		if overlapping_enemies.has(enemy_component):
			overlapping_enemies.erase(enemy_component)
