extends Node

@export var game_scene: PackedScene
@export var main_menu_scene: PackedScene
@export var end_screen_scene: PackedScene

var current_scene: Node = null

func _ready():
    var root = get_tree().root
    current_scene = root.get_child(root.get_child_count() - 1)

func goto_scene(packed_scene: PackedScene):
    if is_instance_valid(current_scene):
        current_scene.queue_free()

    current_scene = packed_scene.instantiate()

    get_tree().root.add_child(current_scene)

func goto_main_menu():
    goto_scene(main_menu_scene)

func start_new_game():
    goto_scene(game_scene)
    current_scene.game_finished.connect(show_end_screen)

func show_end_screen(winner_player_id: int):
    goto_scene(end_screen_scene)
    if current_scene.has_method("set_winner"):
        current_scene.set_winner(winner_player_id)

func quit_game():
    get_tree().quit()
