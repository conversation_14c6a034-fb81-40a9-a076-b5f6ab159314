[gd_scene load_steps=7 format=3 uid="uid://irejsrhvkwb5"]

[ext_resource type="Script" uid="uid://4lodjm1iiq6o" path="res://child_cell.gd" id="1_juaob"]
[ext_resource type="Texture2D" uid="uid://ckcbris45bst6" path="res://units/jelly/harvester.png" id="2_hto10"]
[ext_resource type="Script" uid="uid://67hcr3h2ojbg" path="res://mass_based_scaler_component.gd" id="2_ibugo"]
[ext_resource type="Script" uid="uid://c8akh72pe5wvb" path="res://combat_component.gd" id="3_hto10"]

[sub_resource type="CircleShape2D" id="CircleShape2D_ek05x"]
radius = 102.078

[sub_resource type="CircleShape2D" id="CircleShape2D_hto10"]
radius = 112.071

[node name="ChildCell" type="CharacterBody2D" node_paths=PackedStringArray("combat_component", "scaler_component")]
scale = Vector2(0.5, 0.5)
collision_layer = 0
collision_mask = 0
motion_mode = 1
script = ExtResource("1_juaob")
combat_component = NodePath("CombatComponent")
scaler_component = NodePath("MassBasedScalerComponent")

[node name="MassBasedScalerComponent" type="Node" parent="."]
script = ExtResource("2_ibugo")
metadata/_custom_type_script = "uid://67hcr3h2ojbg"

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_hto10")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_ek05x")

[node name="CombatComponent" type="Area2D" parent="." node_paths=PackedStringArray("combat_owner")]
collision_layer = 0
collision_mask = 0
script = ExtResource("3_hto10")
combat_owner = NodePath("..")
metadata/_custom_type_script = "uid://c8akh72pe5wvb"

[node name="CollisionShape2D" type="CollisionShape2D" parent="CombatComponent"]
shape = SubResource("CircleShape2D_hto10")
