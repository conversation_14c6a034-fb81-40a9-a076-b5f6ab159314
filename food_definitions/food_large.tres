[gd_resource type="Resource" script_class="FoodData" load_steps=3 format=3 uid="uid://food_large_uid"]

[ext_resource type="Script" uid="uid://food_data_uid" path="res://food_data.gd" id="1_fooddata"]
[ext_resource type="Texture2D" uid="uid://food_green_3_uid" path="res://units/food/food_green_3.png" id="2_texture"]

[resource]
script = ExtResource("1_fooddata")
texture = ExtResource("2_texture")
mass_value = 20.0
