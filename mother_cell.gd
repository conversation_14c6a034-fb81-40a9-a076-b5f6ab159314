extends Area2D

@export var player_id: int = 1

@export var start_mass: float = 200.0
@export var combat_component: CombatComponent
@export var scaler_component: MassBasedScalerComponent
var mass: float:
	set(value):
		mass = max(0, value)
		if is_instance_valid(scaler_component):
			scaler_component.update_scale(mass)
		$MassLabel.text = str(int(mass))
		if mass <= 0:
			print("Mother cell died")
			emit_signal("died")
			queue_free()

var charge_time: float = 0.0
var is_charging: bool = false

@export var charge_thresholds: Array[float] = [0.25, 0.75, 1.5, 2.5]
@export var unit_definitions: Array[UnitData]

const ChildCellScene = preload("res://child_cell.tscn")

signal died

func _ready():
	self.mass = start_mass
	scaler_component.initialize(self, start_mass)
	$ChargeBar.max_value = charge_thresholds.back()
	$ChargeBar.value = 0
	$ChargeBar.visible = false

	if player_id == 1:
		collision_layer = 1
		collision_mask = 0
		add_to_group("player1")
		add_to_group("player1_mother")
	else:
		collision_layer = 1 << 1
		collision_mask = 0
		add_to_group("player2")
		add_to_group("player2_mother")

func _process(delta):
	if is_charging:
		charge_time += delta
		$ChargeBar.value = charge_time

	if InputManager.is_action_just_pressed(player_id, "create"):
		is_charging = true
		$ChargeBar.visible = true
		charge_time = 0

	if InputManager.is_action_just_released(player_id, "create"):
		if is_charging:
			spawn_unit()
			is_charging = false
			$ChargeBar.visible = false
			charge_time = 0

func _physics_process(delta: float):
	if is_instance_valid(combat_component):
		combat_component.process_combat(delta)

func spawn_unit():
	var unit_type_index = -1
	if charge_time < charge_thresholds[0]: unit_type_index = 0
	elif charge_time < charge_thresholds[1]: unit_type_index = 1
	elif charge_time < charge_thresholds[2]: unit_type_index = 2
	else: unit_type_index = 3

	if unit_type_index >= unit_definitions.size():
		printerr("Unit definition not found for index: ", unit_type_index)
		return

	var unit_data = unit_definitions[unit_type_index]
	var cost = unit_data.cost

	if mass >= cost:
		self.mass -= cost
		var new_cell = ChildCellScene.instantiate()

		new_cell.initialize(self, unit_data)

		var spawn_offset = Vector2.RIGHT.rotated(randf_range(0, TAU)) * (scale.x * 60)
		new_cell.global_position = global_position + spawn_offset
		get_parent().add_child(new_cell)

func add_mass(amount: float):
	self.mass += amount
