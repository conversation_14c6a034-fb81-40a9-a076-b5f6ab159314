extends Node2D

signal game_finished(winner_player_id: int)

@onready var player1_cell = $Player1Cell
@onready var player2_cell = $Player2Cell
@onready var food_spawn_timer = $FoodSpawnTimer
@onready var virus_spawn_timer = $VirusSpawnTimer

var screen_size

@export var food_definitions: Array[FoodData]

const FoodScene = preload("res://food.tscn")
const VirusScene = preload("res://virus.tscn")

func _ready():
	screen_size = get_viewport_rect().size

	player1_cell.died.connect(_on_player_died.bind(1))
	player2_cell.died.connect(_on_player_died.bind(2))

	food_spawn_timer.timeout.connect(_on_food_spawn_timer_timeout)
	virus_spawn_timer.timeout.connect(_on_virus_spawn_timer_timeout)

func _on_food_spawn_timer_timeout():
	if food_definitions.is_empty():
		printerr("Food Definitions array is empty in Main node. Cannot spawn food.")
		return

	var random_food_data = food_definitions.pick_random()
	var food = FoodScene.instantiate()
	food.initialize(random_food_data)
	food.position = Vector2(randf_range(50, screen_size.x - 50), randf_range(50, screen_size.y - 50))
	add_child(food)

func _on_virus_spawn_timer_timeout():
	var virus = VirusScene.instantiate()
	var spawn_pos = Vector2()
	if randi() % 2 == 0:
		spawn_pos.x = randf_range(0, screen_size.x)
		spawn_pos.y = [0.0, screen_size.y][randi() % 2]
	else:
		spawn_pos.x = [0.0, screen_size.x][randi() % 2]
		spawn_pos.y = randf_range(0, screen_size.y)
	virus.position = spawn_pos
	add_child(virus)

func _on_player_died(player_num):
	print("Player %d died" % player_num)
	var winner_id = 2 if player_num == 1 else 1

	game_finished.emit(winner_id)
