[gd_scene load_steps=4 format=3 uid="uid://cmssssm6ch2jr"]

[ext_resource type="Script" uid="uid://bhdrpx3hfxr4j" path="res://food.gd" id="1_8txok"]
[ext_resource type="Texture2D" uid="uid://d6n4ckxras5a" path="res://units/food/food_green_1.png" id="2_pohk7"]

[sub_resource type="CircleShape2D" id="CircleShape2D_4x0hx"]
radius = 35.0

[node name="Food" type="Area2D" node_paths=PackedStringArray("sprite")]
scale = Vector2(0.2, 0.2)
collision_layer = 4
collision_mask = 0
script = ExtResource("1_8txok")
sprite = NodePath("Sprite2D")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_pohk7")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_4x0hx")
