[gd_scene load_steps=12 format=3 uid="uid://dk8bg4hj0m0ny"]

[ext_resource type="Script" uid="uid://ddvka344p3w2m" path="res://mother_cell.gd" id="1_75siy"]
[ext_resource type="Texture2D" uid="uid://btkugdt1msqpe" path="res://units/jelly/main_cell.png" id="2_0hfub"]
[ext_resource type="Script" uid="uid://bn1vunwhi56ot" path="res://unit_data.gd" id="2_xkjnf"]
[ext_resource type="Script" uid="uid://c8akh72pe5wvb" path="res://combat_component.gd" id="3_0hfub"]
[ext_resource type="Resource" uid="uid://collector" path="res://unit_definitions/collector.tres" id="3_rr0rs"]
[ext_resource type="Resource" uid="uid://defender" path="res://unit_definitions/defender.tres" id="4_3wfa4"]
[ext_resource type="Resource" uid="uid://d1hx4jr813il5" path="res://unit_definitions/hunter.tres" id="5_uiych"]
[ext_resource type="Resource" uid="uid://8epkyvphulid" path="res://unit_definitions/aggressor.tres" id="6_irhjg"]
[ext_resource type="Script" uid="uid://67hcr3h2ojbg" path="res://mass_based_scaler_component.gd" id="9_rr0rs"]

[sub_resource type="CircleShape2D" id="CircleShape2D_6nnu1"]
radius = 209.086

[sub_resource type="CircleShape2D" id="CircleShape2D_fvo0g"]
radius = 229.367

[node name="MotherCell" type="Area2D" node_paths=PackedStringArray("combat_component", "scaler_component")]
script = ExtResource("1_75siy")
combat_component = NodePath("CombatComponent")
scaler_component = NodePath("MassBasedScalerComponent")
unit_definitions = Array[ExtResource("2_xkjnf")]([ExtResource("3_rr0rs"), ExtResource("4_3wfa4"), ExtResource("5_uiych"), ExtResource("6_irhjg")])

[node name="MassBasedScalerComponent" type="Node" parent="."]
script = ExtResource("9_rr0rs")
metadata/_custom_type_script = "uid://67hcr3h2ojbg"

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(1.90735e-06, -1)
texture = ExtResource("2_0hfub")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_6nnu1")

[node name="MassLabel" type="Label" parent="."]
offset_left = -12.0
offset_top = -95.0
offset_right = 28.0
offset_bottom = -72.0
text = "200"

[node name="ChargeBar" type="ProgressBar" parent="."]
offset_top = 75.0
offset_right = 4.0
offset_bottom = 102.0

[node name="CombatComponent" type="Area2D" parent="." node_paths=PackedStringArray("combat_owner")]
collision_layer = 0
collision_mask = 12
script = ExtResource("3_0hfub")
combat_owner = NodePath("..")
metadata/_custom_type_script = "uid://c8akh72pe5wvb"

[node name="CollisionShape2D" type="CollisionShape2D" parent="CombatComponent"]
shape = SubResource("CircleShape2D_fvo0g")
