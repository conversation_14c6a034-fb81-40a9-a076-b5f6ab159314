extends Node

const DOUBLE_TAP_DELAY_SEC: float = 0.3
var player_action_states: Dictionary = {
	1: {"create": {"pressed": false, "just_pressed": false, "just_released": false},
		 "recall": {"pressed": false, "just_pressed": false, "just_released": false}},
	2: {"create": {"pressed": false, "just_pressed": false, "just_released": false},
		 "recall": {"pressed": false, "just_pressed": false, "just_released": false}}
}
var _prev_player_action_states: Dictionary

var _last_tap_time_ms: Dictionary = {1: 0, 2: 0}
var _tap_count: Dictionary = {1: 0, 2: 0}


func _ready():
	_prev_player_action_states = player_action_states.duplicate(true)


func _process(_delta):
	for player_id in player_action_states:
		for action_name in player_action_states[player_id]:
			var current_state = player_action_states[player_id][action_name]
			var prev_state = _prev_player_action_states[player_id][action_name]

			current_state.just_pressed = current_state.pressed and not prev_state.pressed
			current_state.just_released = not current_state.pressed and prev_state.pressed

	_prev_player_action_states = player_action_states.duplicate(true)

	var current_time_ms = Time.get_ticks_msec()
	for player_id in _tap_count:
		if _tap_count[player_id] == 1 and (current_time_ms - _last_tap_time_ms[player_id]) > DOUBLE_TAP_DELAY_SEC * 1000:
			_tap_count[player_id] = 0


func _unhandled_input(event: InputEvent):
	if event is InputEventKey:
		for player_id in [1, 2]:
			for action_name in ["create", "recall"]:
				var input_map_action = "p%d_%s" % [player_id, action_name]
				if event.is_action(input_map_action):
					player_action_states[player_id][action_name].pressed = event.is_pressed()

	if event is InputEventMouseButton or event is InputEventScreenTouch:
		if event.is_pressed() or event.is_released():
			var screen_width = get_viewport().get_visible_rect().size.x
			var player_id = 1 if event.position.x < screen_width / 2 else 2

			player_action_states[player_id]["create"].pressed = event.is_pressed()

			var current_time_ms = Time.get_ticks_msec()

			if event.is_pressed():
				if (current_time_ms - _last_tap_time_ms[player_id]) < DOUBLE_TAP_DELAY_SEC * 1000:
					_tap_count[player_id] += 1
				else:
					_tap_count[player_id] = 1

				_last_tap_time_ms[player_id] = current_time_ms

				if _tap_count[player_id] >= 2:
					player_action_states[player_id]["recall"].pressed = true

			if event.is_released():
				player_action_states[player_id]["recall"].pressed = false

func is_action_pressed(player_id: int, action_name: String) -> bool:
	return player_action_states[player_id][action_name].pressed

func is_action_just_pressed(player_id: int, action_name: String) -> bool:
	return player_action_states[player_id][action_name].just_pressed

func is_action_just_released(player_id: int, action_name: String) -> bool:
	return player_action_states[player_id][action_name].just_released
